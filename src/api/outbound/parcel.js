import request from '@/utils/request'
import axios from 'axios'
import { getToken } from '@/utils/auth'

/**
 * 一件代发出库列表
 * @param {Object} data - 查询参数
 * @returns {Promise} List
 */
export function listing(data) {
  return request({
    url: '/outbound/listing',
    method: 'post',
    data,
  })
}

/**
 * 用户参数
 * @returns {Promise} userParams
 */
export function userParams() {
  return request({
    url: '/outbound/userParams',
    method: 'post',
  })
}

/**
 * 用户参数
 * @returns {Promise} options
 */
export function options() {
  return request({
    url: '/outbound/options',
    method: 'post',
  })
}

/**
 * Search SKU
 * @returns {Promise} list
 */
export function skus(data) {
  return request({
    url: '/outbound/skus',
    method: 'post',
    data,
  })
}

/**
 * 分组状态数量统计
 * @returns {Promise} list
 */
export function statusSum(data) {
  return request({
    url: '/outbound/statusSum',
    method: 'post',
    data,
  })
}

/**
 * 标记异常
 * @returns {Promise} markException
 */
export function makeException(data) {
  return request({
    url: '/outbound/exception',
    method: 'post',
    data,
  })
}

/**
 * 客户列表
 * @returns {Promise} consulerList
 */
export function consumers(data) {
  return request({
    url: '/outbound/consumers',
    method: 'post',
    data,
  })
}

/**
 * 渠道列表
 * @returns {Promise} channelList
 */
export function channels(data) {
  return request({
    url: '/outbound/channels',
    method: 'post',
    data,
  })
}

/**
 * 库位列表
 * @returns {Promise} channelList
 */
export function dimCells(data) {
  return request({
    url: '/outbound/queryDimCell',
    method: 'post',
    data,
  })
}

/**
 * SKU 列表
 * @returns {Promise} skuList
 */
export function skuList(data) {
  return request({
    url: '/outbound/skus',
    method: 'post',
    data,
  })
}

/**
 * 一件代发出库详情
 * @returns {Promise} list
 */
export function detail(data) {
  return request({
    url: '/outbound/detail',
    method: 'post',
    data,
  })
}

/**
 * 一件代发出库详情 - 日志
 * @returns {Promise} list
 */
export function detailLogs(data) {
  return request({
    url: '/outbound/logs',
    method: 'post',
    data,
  })
}

/**
 * 一件代发出库详情 - 面单/附件预览下载地址
 * @returns {Promise} list
 */
export function appendixPreview(data) {
  return request({
    url: '/outbound/appendixPreview',
    method: 'post',
    data,
  })
}

/**
 * 一件代发出库 - 打印发货清单
 * @returns {Promise}
 */
export function shipment(data) {
  return request({
    url: '/outbound/shipment',
    method: 'post',
    data,
    responseType: 'blob', // 指定响应类型为二进制数据
  })
}
