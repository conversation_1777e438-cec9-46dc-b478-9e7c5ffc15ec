<script setup>
import { defineProps } from 'vue'
import {useRouter} from "vue-router";
import { DocumentCopy } from '@element-plus/icons-vue'

const router = useRouter()

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  orderList: {
    type: Array,
    default: () => [],
  },
  orderData: {
    type: Array,
    default: () => [],
  },
})


//复制
const handleCopy = async (val) => {
  await navigator.clipboard.writeText(val);
  ElMessage.success('复制成功')
}


</script>

<template>
  <div class="profile-container" v-if="orderList.length" v-loading="loading">
    <el-form label-width="100px" label-position="left" >

      <el-card class="profile-card" shadow="hover">
        <div class="app-container">
          <el-table :data="orderList" style="width: 100%;">
            <el-table-column label="出库单号" type="index" prop="" width="250px;" show-overflow-tooltip>
              <template #default="scope">
                <span style="font-size: 18px;font-weight: bold;color: black;">
                  {{ scope.row.sourceNo }}
                  <el-link type="primary" v-if="scope.row.sourceNo" style="margin-left: 5px;margin-bottom: 8px;"
                   :icon="DocumentCopy" @click=" handleCopy(scope.row.sourceNo)"></el-link>
                </span>
              </template>
            </el-table-column>
            <el-table-column label="物流跟踪号 / 物流渠道" prop="expressNo"  width="300px;" >
              <template #default="scope">
                <span>
                  {{ scope.row.expressNo }} <br>
                  {{ scope.row.logisticsChannel }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="重量(kg)" prop="" >
              <template #default="scope">
                <span>
                  {{ scope.row.realWeight }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="称重时间" prop="" >
              <template #default="scope">
                <span>
                  {{ scope.row.weighTime }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </el-form>
  </div>
  <div class="defaultTips"  v-else>
    <img src="/scan.svg" />
    <label>
      请扫描出库单号/物流跟踪号
    </label>
  </div>
</template>

<style>
.defaultTips {
  text-align: center;
  color: #606266;
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center;     /* 垂直居中 */
  height: 100vh;
}
</style>
