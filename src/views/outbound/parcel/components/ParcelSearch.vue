<script setup>
import { Search, Refresh, Delete } from '@element-plus/icons-vue'
import { defineProps, defineEmits, ref, reactive } from 'vue'
import DateTypeRangePicker from '@/components/DateTypeRangePicker.vue'
import NumberRange from '@/components/NumberRangeInput.vue'
import { dimCells } from '@/api/outbound/parcel'
import SearchSkuTab from './SearchSkuTab.vue'

const props = defineProps({
  queryParams: {
    type: Object,
    required: true,
  },
  dateTypeOptions: {
    type: Array,
    required: true,
  },
  dateParams: {
    type: Array,
    required: true,
  },
  orderNoOptions: {
    type: Array,
    required: true,
  },
  salesPlatformList:{
    type: Array,
    required: true,
  },
  carrierList:{
    type: Array,
    required: true,
  },
  receiverOptions:{
    type: Array,
    required: true,
  },
  unitMarkOptions:{
    type: Array,
    required: true,
  },
  skuTypeOptions:{
    type: Array,
    required: true,
  },
  consumerList: {
    type: Array,
    required: true,
  },
  countryRegionList: {
    type: Array,
    required: true,
  },
  numberRangeOptions: {
    type: Array,
    required: true,
  },
  numberRangeInput: {
    type: Array,
    required: true,
  },
  channelList: {
    type: Array,
    required: true,
  },
  skuTypeList: {
    type: Array,
    required: true,
  },
})

const emit = defineEmits(['search', 'reset', 'params-sku'])

// 库位相关状态
const cellOptions = ref([])
const cellLoading = ref(false)
const cellSearchTimeout = ref(null)
const selSkusEmp = ref(false)

// Search SKU
const searchSkuInput = ref("");
const searchSkuInputTips = ref("");

// 选择 SKU 弹窗
const skuDialog = reactive({
  visible: false,
  title: '添加产品',
  loading: false
})

// 库位远程搜索方法
const handleCellSearch = async (query) => {
  if (cellSearchTimeout.value) {
    clearTimeout(cellSearchTimeout.value)
  }
  
  // 防抖处理，100ms后执行搜索
  cellSearchTimeout.value = setTimeout(async () => {
    if (!query || query.length < 1) {
      cellOptions.value = []
      return
    }
    
    cellLoading.value = true
    
    try {
      const response = await dimCells({
        cellNoDim: query,
        whCode: 'CA'
      })
      
      if (response && response.code === 200) {
        cellOptions.value = (response.data || []).map(item => ({
          value: item.cellNo,
          label: item.cellNo
        }))
      } else {
        cellOptions.value = []
      }
    } catch (error) {
      console.error('获取库位列表失败:', error)
      cellOptions.value = []
    } finally {
      cellLoading.value = false
    }
  }, 100)
}

const handleSearch = (val) => {

  emit('search')
}

const selVal = ref(null)
const handleSelFocus = (val) => {
  selVal.value = val
}

const handleSelBlur = (val) => {
  if(JSON.stringify(val) !== JSON.stringify(selVal.value)){
    handleSearch()
  }
}

const handleSearchSku = () => {
  skuDialog.visible = true;
}

const handleSearchSkuChange = (val) => {
  if(!val) {
    props.queryParams.skuQtyStrList = [];
    selSkusEmp.value = true;
    handleSearch();
  }
}

const searchSkuInputEmp = () => {
  searchSkuInput.value = '';
  props.queryParams.skuQtyStrList = [];
  selSkusEmp.value = true;
  handleSearch();
}

const checSearchkSku = () => {
  selSkusEmp.value = false;
}

// 提交表单
const handleSelSkuSub = async (selectRows) => {
  skuDialog.loading = true
  try {
    searchSkuInput.value = '';
    searchSkuInputTips.value = '';
    props.queryParams.skuQtyStrList = [];
    selectRows.value.map((item, index) => {
        props.queryParams.skuQtyStrList.push(item.skuQtyStr);
        if(index < 5) {
          searchSkuInput.value += item.skuQtyStr
        }
        searchSkuInputTips.value += item.skuQtyStr;
    })
    if(selectRows.value.length > 5) {
      searchSkuInput.value += ' 等' + selectRows.value.length + '项';
    }


    if(searchSkuInput.value){
      selSkusEmp.value = false;
    }
    handleSearch();

    skuDialog.visible = false
  } catch (error) {
    console.error('操作失败', error)
    ElMessage.error('操作失败')
  } finally {
    skuDialog.loading = false
  }
}

const handleParamsSku = () => {
  emit('params-sku')
}


const handleReset = () => {
  emit('reset')
}
</script>

<template>
  <el-card class="search-card" shadow="hover">
    <el-form :model="queryParams" inline>
      <el-form-item label="" prop="consumer">
        <el-select v-model="queryParams.customerCodes" @focus="handleSelFocus(queryParams.customerCodes)"
                   @blur="handleSelBlur(queryParams.customerCodes)"
                  multiple placeholder="客户名称/代码" style="width: 150px;" filterable>
          <el-option
              v-for="dict in consumerList"
              :key="dict.customerCode"
              :label="dict.customerName"
              :value="dict.customerCode"
          >
          <span style="float: left">{{ dict.customerName }}</span>
            <span
              style="
                float: right;
                color: var(--el-text-color-secondary);
                font-size: 13px;
              "
            >
              {{ dict.customerCode }}
            </span>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="" prop="varietyType">
        <el-select v-model="queryParams.varietyType" @focus="handleSelFocus(queryParams.varietyType)"
                   @blur="handleSelBlur(queryParams.varietyType)"
                   multiple placeholder="订单品种类型" style="width: 150px;">
          <el-option
              v-for="dict in skuTypeList"
              :key="dict.key"
              :label="dict.val"
              :value="dict.key"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="" prop="salesPlatform">
        <el-select v-model="queryParams.salesPlatform" @focus="handleSelFocus(queryParams.salesPlatform)"
                   @blur="handleSelBlur(queryParams.salesPlatform)"
                   multiple placeholder="销售平台" style="width: 150px;" filterable>
          <el-option
              v-for="dict in salesPlatformList"
              :key="dict.value"
              :label="dict.cn"
              :value="dict.value"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="" prop="channel">
        <el-select v-model="queryParams.logisticsChannel" @focus="handleSelFocus(queryParams.logisticsChannel)"
                   @blur="handleSelBlur(queryParams.logisticsChannel)"
                   multiple placeholder="物流渠道" style="width: 150px;" filterable>
          <el-option
              v-for="dict in channelList"
              :key="dict.logisticsChannel"
              :label="dict.logisticsChannelName"
              :value="dict.logisticsChannel"
          >
          <span style="float: left">{{ dict.logisticsChannelName }}</span>
            <span
              style="
                float: right;
                color: var(--el-text-color-secondary);
                font-size: 13px;
              "
            >
              {{ dict.logisticsChannel }}
            </span>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="" prop="carrier">
        <el-select v-model="queryParams.logisticsCarrier" @focus="handleSelFocus(queryParams.logisticsCarrier)"
                   @blur="handleSelBlur(queryParams.logisticsCarrier)"
                   multiple placeholder="承运商" style="width: 150px;" filterable>
          <el-option
              v-for="dict in carrierList"
              :key="dict.value"
              :label="dict.cn"
              :value="dict.value"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="" prop="carrier">
        <el-select v-model="queryParams.countryRegionCodes" @focus="handleSelFocus(queryParams.countryRegionCodes)"
                   @blur="handleSelBlur(queryParams.countryRegionCodes)"
                   multiple placeholder="目的国家" style="width: 150px;" filterable>
          <el-option
              v-for="dict in countryRegionList"
              :key="dict.countryRegionCode"
              :label="dict.name"
              :value="dict.countryRegionCode"
          >
          <span style="float: left">{{ dict.name }}</span>
            <span
              style="
                float: right;
                color: var(--el-text-color-secondary);
                font-size: 13px;
              "
            >
              {{ dict.countryRegionCode }}
            </span>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="" prop="dimCell">
        <el-select-v2 
          v-model="queryParams.cellNos"
          multiple 
          placeholder="库位" 
          style="width: 200px;" 
          filterable
          remote
          :remote-method="handleCellSearch"
          :loading="cellLoading"
          :options="cellOptions"
          clearable
          collapse-tags
          collapse-tags-tooltip
          :max-collapse-tags="1"
        >
        </el-select-v2>
      </el-form-item>

      <el-form-item>
        <el-tooltip class="item" effect="light" :disabled="!searchSkuInput"  :content="searchSkuInputTips" placement="top">
          <el-input
            v-model="searchSkuInput"
            style="width: 170px"
            size="large"
            placeholder="Search SKU"
            clearable
            :suffix-icon="Search"
            show-overflow-tooltip
            @click="handleSearchSku"
            @change="handleSearchSkuChange"
          />
        </el-tooltip>
        
      </el-form-item>

      <el-form-item prop="dateRange" class="search-item">
        <DateTypeRangePicker style="width: 450px"
                             v-model:dateType="dateParams.dateType"
                             v-model:dateRange="dateParams.dateRange"
                             :dateTypeOptions="dateTypeOptions"
                             value-format="YYYY-MM-DD"
        />
      </el-form-item>

      <el-form-item label="" prop="numberRange">
        <el-select v-model="queryParams.numRangeType" placeholder="" style="width: 120px;">
          <el-option
              v-for="dict in numberRangeOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          >
          </el-option>
        </el-select>
          <NumberRange v-model="numberRangeInput.numberRange"
                       v-model:min-value="numberRangeInput.minValue"
                       v-model:max-value="numberRangeInput.maxValue"
                       v-model:slotStyle="plain" @keyup.enter="handleSearch"
                       :valueRange="[0, 1000]">
          </NumberRange>
      </el-form-item>

      <el-form-item prop="receiveSearchValue" class="search-item">
        <el-input
            v-model="queryParams.receiverVal"
            placeholder="请输入搜索内容"
            clearable
            style="width: 240px"
            class="input-with-select"
            @keyup.enter="handleSearch"
        >
          <template #prepend>
            <el-select v-model="queryParams.receiverKey" style="width: 100px">
              <el-option
                  v-for="option in receiverOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
              />
            </el-select>
          </template>
        </el-input>
      </el-form-item>

      <br>
      <el-form-item label="" prop="varietyType">
        <el-select v-model="queryParams.unitMark" @change="handleSearch" placeholder="单位类型" style="width: 150px;">
          <el-option
              v-for="dict in unitMarkOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item prop="skuSearchValue" class="search-item">
        <el-input
            v-model="queryParams.skuVal"
            placeholder="请输入搜索内容"
            clearable
            style="width: 240px"
            class="input-with-select"
            @keyup.enter="handleSearch"
        >
          <template #prepend>
            <el-select v-model="queryParams.skuKey" style="width: 100px">
              <el-option
                  v-for="option in skuTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
              />
            </el-select>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item prop="searchValue" class="search-item">
        <el-input
          v-model="queryParams.sourceNoVal"
          placeholder="请输入搜索内容"
          clearable
          style="width: 240px"
          class="input-with-select"
          @keyup.enter="handleSearch"
        >
          <template #prepend>
            <el-select v-model="queryParams.orderNoType" style="width: 100px">
              <el-option
                v-for="option in orderNoOptions"
                :key="option.key"
                :label="option.val"
                :value="option.key"
              />
            </el-select>
          </template>
        </el-input>
      </el-form-item>

      <br>
      <el-form-item>
        <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
        <el-button :icon="Refresh" @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
    <div>
      <span v-if="searchSkuInput" >
        <!-- <el-tooltip
          class="box-item"
          effect="light"
          :content="searchSkuInputTips"
          placement="top"
        > -->
        <el-tooltip placement="top" effect="light">
          <template #content> {{ searchSkuInputTips }} </template>
        <!-- </el-tooltip> -->
          <el-tag type="primary">SKU*数量：{{searchSkuInput}}</el-tag>
        </el-tooltip>
        
        <el-link type="info"><el-icon @click="searchSkuInputEmp"><delete /></el-icon></el-link>
      </span>
    </div>
    
  </el-card>
  <!-- 批量选择 SKU 弹窗 -->

  <SearchSkuTab :visible="skuDialog.visible" :title="skuDialog.title" :loading="skuDialog.loading" :selSkusEmp="selSkusEmp"
                 @update:visible="(val) => skuDialog.visible = val" @submitTab="handleSelSkuSub" @check-sku="checSearchkSku"/>
  
</template>

<style scoped>
.search-card {
  margin-bottom: 20px;
  border-radius: var(--card-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow); 
  border: none;
}
</style>
