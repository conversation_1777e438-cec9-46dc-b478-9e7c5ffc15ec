<script setup>
import { defineProps, computed } from 'vue'

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  orderList: {
    type: Array,
    default: () => [],
  },
  orderData: {
    type: Array,
    default: () => [],
  },
  settingData: {
    type: Object,
    default: () => ({}),
  },
})

// 计算网格样式
const gridStyle = computed(() => {
  const style = {}
  const minItemWidth = 80
  if (props.settingData?.seedWallType === '1') {
    const mapNum = props.settingData.seedWallMapNum || 1
    const mapType = props.settingData.seedWallMapType

    if (mapType === 'column') {
      // 列模式 - 固定列数
      style.gridTemplateColumns = `repeat(${mapNum}, 1fr)`
    } else if (mapType === 'row') {
      // 行模式 - 固定行数
      const totalItems = props.orderData.length
      const itemsPerRow = Math.ceil(totalItems / mapNum)

      // 使用固定列数，让每行显示相同数量的卡片
      style.gridTemplateColumns = `repeat(${itemsPerRow}, 1fr)`
      style.gridAutoRows = 'minmax(80px, auto)'
    } else {
      // 未指定类型，使用自动填充
      style.gridTemplateColumns = `repeat(auto-fill, minmax(${minItemWidth}px, 1fr))`
    }
  } else {
    // 默认自动填充模式
    style.gridTemplateColumns = `repeat(auto-fill, minmax(${minItemWidth}px, 1fr))`
  }

  return style
})

// 序号点击事件
const handleNumberClick = (item, index) => {
  console.log('点击了序号:', index + 1, '对应数据:', item)
  // 这里可以添加具体的点击处理逻辑
}
</script>

<template>
  <div class="profile-container" v-if="1" v-loading="loading">
    <!-- 上部分：进度信息和提示区域 -->
    <el-card class="top-card" shadow="never">
      <div class="progress-info">
        <span class="progress-item"
          >订单进度 <span class="greey">{{ orderList.length }}</span
          ><span class="info">/ {{ orderList.length }}</span></span
        >
        <span class="progress-item"
          >产品进度 <span class="greey">{{ orderData.length }}</span
          ><span class="info">/ {{ orderData.length }}</span></span
        >
      </div>
      <div class="info-box">
        <div class="info-content">
          <img src="/scan.svg" />
          <label>请扫描商品，并根据提示将商品放置对应格子内</label>
        </div>
      </div>
    </el-card>

    <!-- 下部分：自适应内容展示区域 -->
    <div class="bottom-section">
      <div class="content-grid" :style="gridStyle">
        <el-card
          class="grid-item"
          v-for="(item, index) in orderData"
          :key="item.id || index"
          shadow="hover"
          @click="handleNumberClick(item, index)"
        >
          <div class="card-content">
            <span class="item-number">{{ index + 1 }}</span>
            <div class="progress-display">
              <span class="progress-text">{{ index + 1 }}/{{ orderData.length }}</span>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
  <div class="defaultTips" v-else>
    <img src="/scan.svg" />
    <label>请扫描波次单号开始分拣</label>
  </div>
</template>

<style scoped>
.profile-container {
  display: flex;
  flex-direction: column;
}

.top-card {
  margin-bottom: 20px;
}

.progress-info {
  display: flex;
  justify-content: flex-end;
  gap: 20px;
  margin-bottom: 15px;
}

.progress-item {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}
.progress-item .greey {
  color: #28a864;
  font-weight: 600;
  font-size: 18px;
}
.progress-item .info {
  color: #0b1019;
  font-weight: 600;
  font-size: 18px;
}

.info-box {
  border: 1px solid #e6e8eb;
  border-radius: 4px;
  padding: 40px 20px;
  text-align: center;
  min-height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.info-content {
  text-align: center;
  color: #606266;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  flex-direction: row;
  gap: 16px;
}

.info-content img {
  width: 52px;
  height: 52px;
  opacity: 0.6;
}

.info-content label {
  font-size: 14px;
  color: #909399;
}

.bottom-section {
  flex: 1;
  overflow-y: auto;
}

.content-grid {
  display: grid;
  gap: 16px;
  padding: 10px 0;
}

.grid-item {
  transition: transform 0.2s ease;
  position: relative;
  cursor: pointer;
  min-height: 80px;
}

.grid-item:hover {
  transform: translateY(-2px);
}

.card-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60px;
}

.item-number {
  position: absolute;
  top: -10px;
  left: -10px;
  background: #409eff;
  color: white;
  font-size: 12px;
  font-weight: 600;
  width: 30px;
  height: 20px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  transition: all 0.2s ease;
}

.progress-display {
  text-align: center;
}

.progress-text {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
}

.defaultTips {
  text-align: center;
  color: #606266;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  flex-direction: row;
  gap: 16px;
}

.defaultTips img {
  width: 64px;
  height: 64px;
  opacity: 0.6;
}

.defaultTips label {
  font-size: 16px;
  color: #909399;
}
</style>
